#!/usr/bin/env python3
"""
控制台美化输出工具
提供彩色输出、图标、格式化和简化的日志方法
"""

import sys
from typing import Optional


class ConsoleFormatter:
    """美化控制台输出的工具类"""
    
    # 颜色代码
    COLORS = {
        'red': '\033[91m',
        'green': '\033[92m',
        'yellow': '\033[93m',
        'blue': '\033[94m',
        'purple': '\033[95m',
        'cyan': '\033[96m',
        'white': '\033[97m',
        'gray': '\033[90m',
        'bold': '\033[1m',
        'underline': '\033[4m',
        'reset': '\033[0m'
    }
    
    # 图标
    ICONS = {
        'success': '✅',
        'error': '❌',
        'warning': '⚠️',
        'info': 'ℹ️',
        'thinking': '🤔',
        'executing': '⚡',
        'report': '📋',
        'rocket': '🚀',
        'gear': '⚙️',
        'magnifying': '🔍',
        'clock': '⏰',
        'check': '✓',
        'cross': '✗',
        'arrow': '→',
        'bullet': '•'
    }
    
    @classmethod
    def colored(cls, text: str, color: str) -> str:
        """给文本添加颜色"""
        return f"{cls.COLORS.get(color, '')}{text}{cls.COLORS['reset']}"
    
    @classmethod
    def header(cls, text: str, width: int = 60) -> str:
        """创建标题样式"""
        border = "=" * width
        padding = (width - len(text) - 2) // 2
        centered = f"{' ' * padding}{text}{' ' * padding}"
        if len(centered) < width:
            centered += " "
        return f"\n{cls.colored(border, 'cyan')}\n{cls.colored(centered, 'bold')}\n{cls.colored(border, 'cyan')}\n"
    
    @classmethod
    def section(cls, title: str, icon: str = 'info') -> str:
        """创建章节标题"""
        icon_char = cls.ICONS.get(icon, '•')
        return f"\n{cls.colored(f'{icon_char} {title}', 'blue')}\n{'-' * (len(title) + 4)}"
    
    @classmethod
    def step(cls, round_num: int, thought: str) -> str:
        """格式化步骤信息"""
        return f"{cls.colored(f'第 {round_num} 轮', 'purple')} {cls.ICONS['thinking']} {thought}"
    
    @classmethod
    def command(cls, reason: str, cmd: str) -> str:
        """格式化命令执行信息"""
        return f"  {cls.ICONS['executing']} {cls.colored(reason, 'yellow')}\n  {cls.colored('$', 'green')} {cls.colored(cmd, 'white')}"
    
    @classmethod
    def error(cls, message: str) -> str:
        """格式化错误信息"""
        return f"  {cls.ICONS['error']} {cls.colored(message, 'red')}"
    
    @classmethod
    def success(cls, message: str) -> str:
        """格式化成功信息"""
        return f"  {cls.ICONS['success']} {cls.colored(message, 'green')}"
    
    @classmethod
    def warning(cls, message: str) -> str:
        """格式化警告信息"""
        return f"  {cls.ICONS['warning']} {cls.colored(message, 'yellow')}"
    
    @classmethod
    def info(cls, message: str) -> str:
        """格式化信息"""
        return f"  {cls.ICONS['info']} {cls.colored(message, 'cyan')}"
    
    @classmethod
    def progress_bar(cls, current: int, total: int, width: int = 30) -> str:
        """创建进度条"""
        filled = int(width * current / total)
        bar = '█' * filled + '░' * (width - filled)
        percentage = int(100 * current / total)
        return f"[{cls.colored(bar, 'green')}] {percentage}%"


# 创建默认的全局格式化实例
formater = ConsoleFormatter()


class LogPrinter:
    """简化的日志输出类"""
    
    def __init__(self, prefix: str = "", show_timestamp: bool = False):
        """
        初始化日志器
        
        Args:
            prefix: 日志前缀
            show_timestamp: 是否显示时间戳
        """
        self.prefix = prefix
        self.show_timestamp = show_timestamp
    
    def _format_message(self, level: str, message: str, icon: str = "") -> str:
        """格式化日志消息"""
        parts = []
        
        if self.show_timestamp:
            from datetime import datetime
            timestamp = datetime.now().strftime("%H:%M:%S")
            parts.append(ConsoleFormatter.colored(f"[{timestamp}]", 'gray'))

        if self.prefix:
            parts.append(ConsoleFormatter.colored(f"[{self.prefix}]", 'blue'))
        
        if icon:
            parts.append(icon)
        
        parts.append(message)
        
        return " ".join(parts)
    
    def header(self, text: str):
        """输出标题"""
        print(ConsoleFormatter.header(text))

    def section(self, title: str, icon: str = 'info'):
        """输出章节标题"""
        print(ConsoleFormatter.section(title, icon))

    def info(self, message: str):
        """输出信息日志"""
        formatted = self._format_message("INFO", message, ConsoleFormatter.ICONS['info'])
        print(formatted)
    
    def success(self, message: str):
        """输出成功日志"""
        formatted = self._format_message("SUCCESS", 
                                       ConsoleFormatter.colored(message, 'green'), 
                                       ConsoleFormatter.ICONS['success'])
        print(formatted)
    
    def warning(self, message: str):
        """输出警告日志"""
        formatted = self._format_message("WARNING", 
                                       ConsoleFormatter.colored(message, 'yellow'), 
                                       ConsoleFormatter.ICONS['warning'])
        print(formatted)
    
    def error(self, message: str):
        """输出错误日志"""
        formatted = self._format_message("ERROR", 
                                       ConsoleFormatter.colored(message, 'red'), 
                                       ConsoleFormatter.ICONS['error'])
        print(formatted, file=sys.stderr)
    
    def debug(self, message: str):
        """输出调试日志"""
        formatted = self._format_message("DEBUG", 
                                       ConsoleFormatter.colored(message, 'gray'), 
                                       ConsoleFormatter.ICONS['bullet'])
        print(formatted)
    
    def step(self, step_num: int, message: str):
        """输出步骤日志"""
        formatted = self._format_message("STEP", 
                                       f"{ConsoleFormatter.colored(f'第 {step_num} 步', 'purple')} {message}", 
                                       ConsoleFormatter.ICONS['arrow'])
        print(formatted)
    
    def command(self, cmd: str, reason: str = ""):
        """输出命令执行日志"""
        if reason:
            self.info(f"{ConsoleFormatter.colored(reason, 'yellow')}")
        formatted = self._format_message("CMD", 
                                       f"{ConsoleFormatter.colored('$', 'green')} {ConsoleFormatter.colored(cmd, 'white')}", 
                                       ConsoleFormatter.ICONS['executing'])
        print(formatted)
    
    def result(self, success: bool, message: str, duration_ms: Optional[int] = None):
        """输出执行结果日志"""
        if success:
            result_msg = ConsoleFormatter.colored("✓ 成功", 'green')
        else:
            result_msg = ConsoleFormatter.colored("✗ 失败", 'red')
        
        if duration_ms is not None:
            result_msg += f" ({duration_ms}ms)"
        
        if message:
            result_msg += f" - {message}"
        
        formatted = self._format_message("RESULT", result_msg)
        print(f"    {formatted}")


# 创建默认的全局日志器实例
log = LogPrinter()

# 创建带前缀的专用日志器
def create_log_printer(prefix: str = "", show_timestamp: bool = False) -> LogPrinter:
    """创建自定义日志器"""
    return LogPrinter(prefix, show_timestamp)


if __name__ == "__main__":
    # 测试输出效果
    log.header("🧪 控制台格式化测试")
    
    # 测试基本日志方法
    log.info("这是一条信息日志")
    log.success("操作成功完成")
    log.warning("这是一个警告")
    log.error("这是一个错误")
    log.debug("调试信息")
    
    print()
    log.step(1, "开始执行第一步")
    log.command("kubectl get nodes", "检查集群节点状态")
    log.result(True, "找到 3 个节点", 245)
    
    log.step(2, "检查Pod状态")
    log.command("kubectl get pods --all-namespaces")
    log.result(False, "连接被拒绝", 1200)
    
    # 测试自定义日志器
    log.section("自定义日志器测试", "gear")
    k8s_log = create_log_printer("K8S", True)
    k8s_log.info("集群诊断开始")
    k8s_log.success("节点状态正常")
    k8s_log.warning("发现网络问题")

    log.header('🎉 测试完成')
