from typing import List, Dict, Any
from datetime import datetime

class MarkdownReporter:
    """生成 Markdown 格式的诊断报告"""

    @staticmethod
    def generate_report(report_data: Dict[str, Any], audit_data: List[Dict[str, Any]]) -> str:
        """生成完整的 Markdown 诊断报告"""
        if not report_data:
            return "# 诊断报告\n\n❌ 未能生成有效的诊断报告"

        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        md_content = f"""# 🔍 Kubernetes 集群诊断报告

**生成时间**: {timestamp}
**诊断状态**: ✅ 完成

---

## 📋 执行摘要

{report_data.get('summary', '未提供摘要信息')}

---

## 🎯 根本原因分析

{report_data.get('root_cause', '未能确定根本原因')}

---

## 📊 影响范围

{report_data.get('impact', '影响范围未明确')}

---

## 🛠️ 建议修复步骤

"""

        next_steps = report_data.get('next_steps', [])
        if next_steps:
            for i, step in enumerate(next_steps, 1):
                md_content += f"{i}. {step}\n"
        else:
            md_content += "暂无具体修复建议\n"

        md_content += "\n---\n\n## 📎 诊断过程附录\n\n"

        # 添加诊断过程概览
        if audit_data:
            md_content += f"**总诊断轮次**: {len(audit_data)} 轮  \n"
            total_commands = sum(len(round_data.get('cmd_results', [])) for round_data in audit_data)
            md_content += f"**执行命令总数**: {total_commands} 条  \n\n"

            # 添加每轮的简要信息
            md_content += "### 诊断过程概览\n\n"
            for round_data in audit_data:
                round_num = round_data.get('round', 0)
                thought = round_data.get('plan', {}).get('thought', '无思考记录')
                cmd_count = len(round_data.get('cmd_results', []))
                md_content += f"- **第 {round_num} 轮**: {thought} ({cmd_count} 条命令)\n"

        # 添加证据引用
        appendix = report_data.get('appendix', [])
        if appendix:
            md_content += "\n### 关键证据引用\n\n"
            for evidence in appendix:
                md_content += f"- {evidence}\n"

        md_content += f"\n---\n\n*报告由 K8S 诊断专家自动生成于 {timestamp}*"

        return md_content

    @staticmethod
    def save_report(content: str, filename: str = None) -> str:
        """保存报告到文件"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"report/k8s_diagnosis_report_{timestamp}.md"

        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(content)
            return filename
        except Exception as e:
            print(f"保存报告失败: {e}")
            return ""
        

def create_reporter():
    return MarkdownReporter()

# 创建全局reporter
reporter = create_reporter()